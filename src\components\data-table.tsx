import { <PERSON><PERSON><PERSON>mo<PERSON>, IBasemodelController, ListResponse } from "@/models/base-model";
import { Empty, Table, theme } from "antd";
import React, { useEffect } from "react";
import type { TableColumnsType } from 'antd';

export interface DataTableParams<T extends IBasemodel, TC extends IBasemodelController<T, any>> {
    mutateItems?: T[];
    controller: TC;
    itemUpdateInterval : number;
    tableColumns : TableColumnsType<T>;
    noDataDetails? : (isError : boolean) => React.JSX.Element;
    invalidate? : boolean;
    onItemsValidatingChange? : (isValidating : boolean, validatingIdList : string[]) => void;
    onDataChange? : (entries : T[]) => void;
    shouldInvalidate : (entry : T) => boolean;
    rowKey?: string | ((record : T) => string);
}

interface ListRequest{
    count: number;
    nextToken?: string;
}

export default function DataTable<T extends IBasemodel, TC extends IBasemodelController<T, any>>(params : DataTableParams<T, TC>){
    const [kbQueryParams, setKbQueryParams] = React.useState<ListRequest>({ count: -1 });
    const { data, error, isLoading, mutate } = params.controller.useList(kbQueryParams);
    const [pendingData, setPendingData] = React.useState<string[]>([]);
    const { data: statusData, isValidating: statusIsLoading } = params.controller.useGet(pendingData, { refreshInterval: params.itemUpdateInterval, revalidateIfStale: true });

    useEffect(() => {
        if(params.onItemsValidatingChange) params.onItemsValidatingChange(statusIsLoading, pendingData)
    }, [statusIsLoading, pendingData])

    useEffect(() => {
        if(params.invalidate === undefined) return;
        mutate();
    }, [params.invalidate])

    useEffect(() => {
        if (params.mutateItems === undefined || params.mutateItems.length == 0) return;
        mutate(async (data: ListResponse<T> | undefined) => {
            if (data === undefined || params.mutateItems === undefined || params.mutateItems.length == 0) return data;

            const mutatedIds = params.mutateItems.map((item: T) => params.controller.getId(item))
            const filteredData = (data.entries ?? []).filter((item: T) => !mutatedIds.includes( params.controller.getId(item)))
            filteredData.push(...params.mutateItems);
            return { ...data, entries: filteredData };
        }, { revalidate: false })
    }, [params.mutateItems])


    useEffect(() => {
        const pendingDataUpdate = (data?.entries ?? []).filter((value: T) => params.shouldInvalidate(value)).map((value: T) =>  params.controller.getId(value)) ?? [];
        setPendingData(pendingData.filter((key: string) => pendingDataUpdate.includes(key)));

        const interval = setTimeout(() => {
            setPendingData(pendingDataUpdate);
        }, params.itemUpdateInterval);

        if(params.onDataChange) params.onDataChange(data?.entries ?? []);
        return () => clearTimeout(interval);
    }, [data])

    useEffect(() => {
        const pendingDataUpdate = statusData?.filter((value: T) => params.shouldInvalidate(value)).map((value: T) =>  params.controller.getId(value)) ?? [];
        const updatedKbs = statusData?.filter((value: T) => !params.shouldInvalidate(value)) ?? [];
        const updatedKbsIds = updatedKbs.map((item: T) =>  params.controller.getId(item));

        if (updatedKbs.length > 0 && (statusData ?? []).length > 0) {
            mutate(async (data: ListResponse<T> | undefined) => {
                if (data === undefined) return data;

                const dataMinusUpdates = (data.entries ?? []).filter((item: T) => !updatedKbsIds.includes( params.controller.getId(item)))
                dataMinusUpdates.push(...updatedKbs);

                return { ...data, entries: dataMinusUpdates };

            }, { revalidate: false })
        }

        if (pendingDataUpdate.length > 0 && (statusData ?? []).filter((arr1Item: T) => !pendingDataUpdate.includes( params.controller.getId(arr1Item))).length > 0) {
            const interval = setInterval(() => {
                if (pendingDataUpdate.length > 0) {
                    setPendingData(pendingDataUpdate);
                }
            }, params.itemUpdateInterval);
            return () => clearInterval(interval);
        }
    }, [statusData])

    return (
        <>
            <Table<T>
                rowSelection={{ type: 'checkbox' }}
                loading={isLoading}
                columns={params.tableColumns}
                dataSource={data?.entries}
                rowKey={params.rowKey ?? 'id'}
                pagination={{ position: ['bottomCenter'] }}
                locale={{ emptyText: <Empty description="No Data">{(params.noDataDetails) ? params.noDataDetails(error) : null}</Empty> }}
            />
        </>
    )
}
"use client";

import { addEdge, Background, BackgroundV<PERSON>t, <PERSON>s, MiniMap, ReactFlow, ReactFlowProvider, useEdgesState, useNodesState, useReactFlow, Edge, Connection } from "@xyflow/react";
import { <PERSON><PERSON>, <PERSON><PERSON>r, Drawer, Flex, Skeleton, Typography } from "antd";
import { DnDContextType, useDnDContext } from "./dnd-context";
import { FieldModel } from "@/components/flowbuilder/variables/field-model";
import React from "react";
import { BaseNode, BaseNodeData, BaseNodeFormElement, BaseNodeProps } from "@/components/behaviortree/node/base-node";
import ViewNodeDrawer from "./drawers/builder-viewer";
import '@xyflow/react/dist/style.css';
import './flow-builder.css';
import ComponentMenu from "./component-menu";
import { NodeComponentInformation, NodeTypes } from "./types";


export interface FlowBuilderProps {
    nodes: BaseNode<any>[];
    connections: [];
    save: (nodes: BaseNode<any>[], edges: [], fields: FieldModel[]) => boolean;
    nodeTypes: NodeTypes;
    context: React.Context<DnDContextType<any>>;
}

interface DrawerProperties {
    isOpen: boolean;
    nodeId?: string;
    title?: string;
    components?: NodeComponentInformation<any>;
}


const FlowBuilder: React.FC<FlowBuilderProps> = (params: FlowBuilderProps) => {
    
    const [fields, setFields] = React.useState<FieldModel[]>([]);
    const [drawer, setDrawer] = React.useState<DrawerProperties>({ isOpen: false });

    const reactFlowWrapper = React.useRef(null);
    const [nodes, setNodes, onNodesChange] = useNodesState(params.nodes);
    const [edges, setEdges, onEdgesChange] = useEdgesState(params.connections);
    const { screenToFlowPosition } = useReactFlow();
    const [type] = useDnDContext(params.context);

    const onCloseDrawer = () => {
        setDrawer({ isOpen: false });
    };

    /*
    FLOW FUNCTIONS
    */
    const onConnect = React.useCallback(
        (connection: Connection) => setEdges((eds) => addEdge(connection, eds)),
        [setEdges],
    );

    const onDragOver = React.useCallback((event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        if (event.dataTransfer) event.dataTransfer.dropEffect = 'move';
    }, []);

    const onOpenDrawer = (nodeId : string, componentType : keyof typeof params.nodeTypes) => {
        setDrawer({
            isOpen: true,
            components: params.nodeTypes[componentType],
            nodeId: nodeId,
            title: 'test title'
        })
    }

    const onDrop = React.useCallback((event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();

        if (!type) {
            return;
        }

        const position = screenToFlowPosition({
            x: event.clientX,
            y: event.clientY,
        });

        const nodeId = crypto.randomUUID();

        const newNode = {
            id: nodeId,
            type: type as string,
            position,
            data: {
                baseData: {
                    name: `${type} node`,
                    nodeData: params.nodeTypes[type].initializer() as typeof params.nodeTypes[typeof type]
                },
                events : {
                    onOpenDrawer: () => {onOpenDrawer(nodeId, type);}
                }
            }
        };

        setNodes(nodes => [...nodes, newNode]);
    }, [screenToFlowPosition, type])

    return (
        <>

            <Drawer
                title={drawer.title}
                open={drawer.isOpen}
                onClose={onCloseDrawer}
                size="large"
            >
                    {(drawer.components !== undefined && drawer.nodeId !== undefined) ? (
                <ViewNodeDrawer 
                    nodeId={drawer.nodeId} 
                    nodes={nodes} 
                    fields={fields} 
                    nodeDescription={drawer.components.description} 
                    nodeForm={drawer.components.form} 
                    onUpdateNode={(newData: BaseNodeData<any>) => {
                        setNodes(nds => nds.map(n => ((n.id === drawer.nodeId) ? {...n, data: {baseData: newData, events: n.data.events}} : n)))
                    } } 
                />) : null }
            </Drawer>
            <Flex style={{ height: '100%' }} className="dndflow" justify='stretch'>
                <div className="reactflow-wrapper" ref={reactFlowWrapper}>
                    <ReactFlow
                        nodes={nodes}
                        edges={edges}
                        fitView
                        nodeTypes={Object.fromEntries(Object.entries(params.nodeTypes).map(([key, value]) => [key, value.node]))}
                        defaultEdgeOptions={{animated: true}}
                        onNodesChange={onNodesChange}
                        onEdgesChange={onEdgesChange}
                        onConnect={onConnect}
                        onDrop={onDrop}
                        onDragOver={onDragOver}
                    >
                        <Controls />
                        <MiniMap />
                        <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
                    </ReactFlow>
                </div>
                <Button onClick={() => {
                    console.log(nodes);
                    console.log(edges);
                    console.log(fields);
                }}>Save</Button>
                <ComponentMenu 
                    onChangeField={(values: FieldModel) => {
                        setFields((fds) => {
                            const ret = [...fds.filter(fd => fd.id != values.id)]
                            ret.push(values)
                            return ret
                        })
                    }}
                    fields={fields}
                    menuItems={params.nodeTypes}
                    context={params.context}
                />
            </Flex>
        </>
    )
}

export default FlowBuilder;
import { APIRoutes } from "@/constants";
import { Fetcher } from "@/functions/fetcher";
import { SWRMutationConfiguration } from "swr/mutation";
import { IBasemodel, IBasemodelController, ListRequest, ListResponse } from "./base-model";
import { KeyedMutator, SWRConfiguration } from "swr";
import { Fetcher2 } from "@/functions/fetcher2";

export enum KnowledgeBaseStatus {
    QUEUED = "QUEUED",
    DB_ENTRY_CREATED = "DB_ENTRY_CREATED",
    KB_CREATED = "KB_CREATED",
    DATASOURCE_CREATED = "DATASOURCE_CREATED",
    READY = "READY",
    SEALING = "SEALING",
    DEPLOYING = "DEPLOYING",
    SYNCRONIZING = "SYNCRONIZING",
    TAGGING = "TAGGING",
    DISABLED = "DISABLED",
}

export interface KnowledgeBase extends IBasemodel {
    kbId: string;
    status: KnowledgeBaseStatus;
    name: string;
    description: string;
    tag: string;
}

export interface ListKnowledgeBaseRequest {
    count: number;
    nextToken?: string;
}

export interface ListKnowledgeBaseResponse {
    entries: KnowledgeBase[];
    nextToken?: string;
    total: number;
}

export interface CreateKnowledgeBaseRequest {
    name: string;
    description: string;
}

export interface UpdateKnowledgeBaseRequest {
    name: string;
    description: string;
}

export function listKnowledgebases(kbQueryParams: ListKnowledgeBaseRequest) {
    const { data, error, isLoading, mutate } = Fetcher.fetcherSWR<ListKnowledgeBaseResponse>(
        APIRoutes.GetURL(APIRoutes.KnowledgeBaseController.LIST),
        {
            method: 'GET',
            auth: true,
            queryString: kbQueryParams
        },
        {
            errorRetryCount: 3,
            revalidateOnFocus: false,
            revalidateOnReconnect: false,
        })

    return { data, error, isLoading, mutate };
}

export function getKnowledgebase(kbIds: string[], options: SWRConfiguration = {}) {
    const { data, error, isLoading, isValidating, mutate } = Fetcher.SWRFetcherMulti<KnowledgeBase>(
        kbIds.map((kbId: string) => APIRoutes.GetURLString(APIRoutes.KnowledgeBaseController.GET).replace("{kbId}", kbId)),
        {
            method: 'GET',
            auth: true,
        },
        {
            errorRetryCount: 0,
            revalidateOnFocus: false,
            revalidateOnReconnect: false,
            revalidateIfStale: false,
            ...options
        })

    return {
        data: data,
        error: !!error,
        isValidating: isValidating,
        isLoading: (!data && !error) || isLoading,
        mutate
    }
}


export function createKnowledgeBase() {
    const { data, error, trigger, isMutating } = Fetcher.SWRMutation<KnowledgeBase>(
        APIRoutes.GetURLString(APIRoutes.KnowledgeBaseController.CREATE),
        { method: 'POST', auth: true },
        {}
    );

    return { data, error, trigger, isMutating };
}

export function updateKnowledgeBase() {
    const { data, error, trigger, isMutating } = Fetcher.SWRMutation<KnowledgeBase>(
        APIRoutes.GetURLString(APIRoutes.KnowledgeBaseController.UPDATE),
        { method: 'PUT', auth: true },
        {}
    );

    return { data, error, trigger, isMutating };
}

export function putKnowledgeBase(kbId?: string, swrmutation_options: SWRMutationConfiguration<any, any, [string], { data: CreateKnowledgeBaseRequest & UpdateKnowledgeBaseRequest; }, any> = {}) {
    const { data, error, trigger, isMutating } = Fetcher.SWRMutation<CreateKnowledgeBaseRequest & UpdateKnowledgeBaseRequest>(
        APIRoutes.GetURLString(APIRoutes.KnowledgeBaseController.UPDATE),
        { method: 'PUT', auth: true, urlPlaceholders: { kbId: kbId } },
        swrmutation_options
    );
    return { data, error, trigger, isMutating };
}

export interface KnowledgeBaseDeployRequest{
    tag : string;
}

export enum KnowledgebaseActions {
    EDIT,
    DELETE,
    DEPLOY,
    ASSIGN_AGENT,
    UPLOAD_DATA
}

export class KnowledgebaseController implements IBasemodelController<KnowledgeBase, KnowledgebaseActions> {
    
    
    getId: (item: KnowledgeBase) => string = (item: KnowledgeBase) => item.kbId;
    useUpdate = (kbId?: string) => Fetcher2.SWRMutationTemplate<KnowledgeBase>(APIRoutes.KnowledgeBaseController.UPDATE, { method: 'PUT', urlPlaceholders: { kbId: kbId } });

    useList = (request: ListRequest) => Fetcher2.SWRTemplate<ListResponse<KnowledgeBase>>(APIRoutes.KnowledgeBaseController.LIST, { method: "GET", queryString: request },
        {
            errorRetryCount: 3,
            revalidateOnFocus: false,
            revalidateOnReconnect: false,
        })

    useGet = (ids: string[], options?: SWRConfiguration) => Fetcher2.SWRMultiTemplate<KnowledgeBase>(
        ids.map(id => APIRoutes.KnowledgeBaseController.GET.replace("{kbId}", id)),
        {
            method: 'GET'
        },
        {
            errorRetryCount: 0,
            revalidateOnFocus: false,
            revalidateOnReconnect: false,
            revalidateIfStale: false,
            ...options
        })

    useDeploy = (kbId : string) => Fetcher2.SWRMutationTemplate<KnowledgeBase>(APIRoutes.KnowledgeBaseController.DEPLOY, {method: 'POST', urlPlaceholders: {kbId: kbId}})

    useDelete = (kbId : string) => Fetcher2.SWRMutationTemplate<KnowledgeBase>(APIRoutes.KnowledgeBaseController.DELETE, {method: 'DELETE', urlPlaceholders: {kbId: kbId}})

    can = (action: KnowledgebaseActions, onItem: KnowledgeBase) => {
        return onItem.status == KnowledgeBaseStatus.READY
    };

    useListFromAgent = (request: ListRequest, agentId: string) => Fetcher2.SWRTemplate<ListResponse<KnowledgeBase>>(
            APIRoutes.AgentController.LIST_KNOWLEDGEBASES_FOR_AGENT,
            { method: 'GET', urlPlaceholders: { agentId: agentId }, queryString: request }
        )
}
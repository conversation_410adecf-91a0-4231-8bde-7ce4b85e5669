"use client";

import { addE<PERSON>, Background, BackgroundV<PERSON>t, <PERSON>s, MiniMap, <PERSON>act<PERSON><PERSON>, ReactFlowProvider, useEdgesState, useNodesState, useReact<PERSON>low, Edge, Connection } from "@xyflow/react";
import { <PERSON><PERSON><PERSON>, <PERSON>er, <PERSON>lex, Skeleton, Typography } from "antd";
import { Dn<PERSON><PERSON><PERSON>, NodeDataTypes, useDnD } from "./DnDContext";
import ComponentsMenu from "./components-menu";
import { FieldModel } from "@/components/flowbuilder/variables/field-model";
import React from "react";
import { BehaviorTreeController } from "@/models/behaviortree";
import { BaseNode, BaseNodeData, BaseNodeFormElement } from "@/components/behaviortree/node/base-node";
import { SetAgentNodeName } from "@/components/behaviortree/node/setagent/set-agent-node";
import SetAgentNode from '@/components/behaviortree/node/setagent/set-agent-node';
import ViewNodeDrawer from "./drawers/builder-viewer";
import SetA<PERSON>Form from "@/components/behaviortree/node/setagent/set-agent-form";
import SetAgentNodeDescription from "@/components/behaviortree/node/setagent/set-agent-description";
import '@xyflow/react/dist/style.css';
import './builder.css';
import { TaskNodeName } from "@/components/behaviortree/node/task/task-node";
import TaskNodeDescription from "@/components/behaviortree/node/task/task-node-description";
import TaskNodeForm from "@/components/behaviortree/node/task/task-node-form";
import TaskNode from "@/components/behaviortree/node/task/task-node";
import { InitializeTaskNodeModelData } from "@/components/behaviortree/node/task/task-node-model";

interface BehaviorTreeBuilderProps {
    nodes : BaseNode<any>[];
    connections : [];
    save : (nodes : BaseNode<any>[], edges : [], fields : FieldModel[]) => boolean;
}



interface NodeComponentType {
    description: React.ComponentType<BaseNodeData<any>>;
    form: React.ComponentType<BaseNodeFormElement<any>>;
    initializer: () => any;
}

const nodeComponents: { [id: string]: NodeComponentType; } = {
    [SetAgentNodeName]: {
        description: SetAgentNodeDescription,
        form: SetAgentForm,
        initializer: () => {return {}}
    },
    [TaskNodeName]: {
        description: TaskNodeDescription,
        form: TaskNodeForm,
        initializer: InitializeTaskNodeModelData
    }
};

interface DrawerProperties {
    isOpen: boolean;
    nodeId?: string;
    title?: string;
    components?: NodeComponentType;
}

const BehaviorTreeBuilder: React.FC<BehaviorTreeBuilderProps> = (params: BehaviorTreeBuilderProps) => {
    
    const [fields, setFields] = React.useState<FieldModel[]>([]);
    const [drawer, setDrawer] = React.useState<DrawerProperties>({ isOpen: false });

    const reactFlowWrapper = React.useRef(null);
    const [nodes, setNodes, onNodesChange] = useNodesState(params.nodes);
    const [edges, setEdges, onEdgesChange] = useEdgesState(params.connections);
    const { screenToFlowPosition } = useReactFlow();
    const [type] = useDnD();

    const nodeTypes = React.useMemo(() => ({
        [SetAgentNodeName]: SetAgentNode,
        [TaskNodeName]: TaskNode
    }), []);

    const onCloseDrawer = () => {
        setDrawer({ isOpen: false });
    };

    /*
    FLOW FUNCTIONS
    */
    const onConnect = React.useCallback(
        (connection: Connection) => setEdges((eds) => addEdge(connection, eds)),
        [setEdges],
    );

    const onDragOver = React.useCallback((event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        if (event.dataTransfer) event.dataTransfer.dropEffect = 'move';
    }, []);

    const onOpenDrawer = (nodeId : string, componentType : keyof typeof NodeDataTypes) => {
        setDrawer({
            isOpen: true,
            components: nodeComponents[componentType],
            nodeId: nodeId,
            title: 'test title'
        })
    }

    const onDrop = React.useCallback((event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();

        if (!type) {
            return;
        }

        const position = screenToFlowPosition({
            x: event.clientX,
            y: event.clientY,
        });

        const nodeId = crypto.randomUUID();

        const newNode = {
            id: nodeId,
            type: type as string,
            position,
            data: {
                baseData: {
                    name: `${type} node`,
                    nodeData: nodeComponents[type].initializer() as typeof NodeDataTypes[typeof type]
                },
                events : {
                    onOpenDrawer: () => {onOpenDrawer(nodeId, type);}
                }
            }
        };

        setNodes(nodes => [...nodes, newNode]);
    }, [screenToFlowPosition, type])

    return (
        <>

            <Drawer
                title={drawer.title}
                open={drawer.isOpen}
                onClose={onCloseDrawer}
                size="large"
            >
                    {(drawer.components !== undefined && drawer.nodeId !== undefined) ? (
                <ViewNodeDrawer 
                    nodeId={drawer.nodeId} 
                    nodes={nodes} 
                    fields={fields} 
                    nodeDescription={drawer.components.description} 
                    nodeForm={drawer.components.form} 
                    onUpdateNode={(newData: BaseNodeData<any>) => {
                        setNodes(nds => nds.map(n => ((n.id === drawer.nodeId) ? {...n, data: {baseData: newData, events: n.data.events}} : n)))
                    } } 
                />) : null }
            </Drawer>
            <Flex style={{ height: '100%' }} className="dndflow" justify='stretch'>
                <div className="reactflow-wrapper" ref={reactFlowWrapper}>
                    <ReactFlow
                        nodes={nodes}
                        edges={edges}
                        fitView
                        nodeTypes={nodeTypes}
                        defaultEdgeOptions={{animated: true}}
                        onNodesChange={onNodesChange}
                        onEdgesChange={onEdgesChange}
                        onConnect={onConnect}
                        onDrop={onDrop}
                        onDragOver={onDragOver}
                    >
                        <Controls />
                        <MiniMap />
                        <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
                    </ReactFlow>
                </div>
                <ComponentsMenu onChangeField={(values: FieldModel) => {
                    setFields((fds) => {
                        const ret = [...fds.filter(fd => fd.id != values.id)]
                        ret.push(values)
                        return ret
                    })
                }}
                    fields={fields}
                />
            </Flex>
        </>
    )
}


export default ({ id }: { id: string }) => {
    const behaviorTreeController = new BehaviorTreeController();
    const { data: behaviorTreeData, isLoading: isLoadingBehaviorTree } = behaviorTreeController.useGet([id as string]);

    const onSave = (nodes : BaseNode<any>[], edges : [], fields : FieldModel[]) => {
        console.log('save');
        return true;
    }

    if (isLoadingBehaviorTree || !behaviorTreeData) {
        return <Skeleton />
    }

    return (
        <>
            <Flex vertical justify='stretch' style={{ height: '100%' }}>
                <Typography.Title level={2}>
                    Behavior Builder for 'asd'
                </Typography.Title>
                <Divider />
                <ReactFlowProvider>
                    <DnDProvider>
                        <BehaviorTreeBuilder nodes={[]} connections={[]} save={onSave} />
                    </DnDProvider>
                </ReactFlowProvider>
            </Flex>
        </>
    );
}
import DrawerTemplate, { DrawerFormTemplateProperties, DrawerTemplateErrorEmpty, makeDrawerFormFields } from "@/templates/drawer-template";
import { Button, Form, Input, Typography } from "antd";
import React, { useEffect } from "react";
import '@ant-design/v5-patch-for-react-19';
import { Fetcher } from "@/functions/fetcher";
import { Agent, AgentController } from "@/models/agent";
import SubmitButton from "@/components/submit-button";

interface AgentEditDrawerParams {
    isOpen: boolean;
    agent?: Agent;
    onSuccess: (data: Agent) => void;
    mode: 'edit' | 'create';
}

const AgentEditDrawer: React.FC<AgentEditDrawerParams> = (params: AgentEditDrawerParams) => {
    const agentController: AgentController = new AgentController();

    const [form] = Form.useForm<Agent>();
    const [drawerError, setDrawerError] = React.useState(DrawerTemplateErrorEmpty)
    const { data, trigger, isMutating } = agentController.useUpdate(params.agent?.agentId);
    const [fieldsData, setFieldsData] = React.useState<any[]>([]);

    useEffect(() => {
        if (data === undefined) return;
        setDrawerError(DrawerTemplateErrorEmpty);
        if (params.onSuccess != null) params.onSuccess(data);
    }, [data])

    useEffect(() => {
        if (params.isOpen) {
            form.resetFields();
            setDrawerError(DrawerTemplateErrorEmpty);
            setFieldsData(makeDrawerFormFields(params.agent));
        }
    }, [params.isOpen])

    const onFinish = (values: Agent) => {
        let request = { name: values.name, description: values.description, instructions: values.instructions }
        trigger({ body: request }).catch((reason: Fetcher.FetcherError) => {
            setDrawerError({ message: reason.message, title: 'Something went wrong with your request.', show: true });
        });
    };

    return (
        <DrawerTemplate
            error={drawerError}
            isLoading={isMutating}
            title={(params.mode === 'create') ? 'Create an Agent' : 'Editing ' + params.agent?.name}
        >
            <Form form={form} fields={fieldsData} layout='vertical' onFinish={onFinish} scrollToFirstError={{ behavior: 'instant', block: 'end', focus: true }}>
                <Form.Item
                    label="Name"
                    name="name"
                    rules={[{ max: 64, message: 'Please input up to 64 characters.' }, { min: 3, message: 'Your agent name must have at least 3 characters.' }, { required: true, message: 'Please input a name for the agent' }]}
                >
                    <Input />
                </Form.Item>
                <Form.Item
                    name="description"
                    label="Description"
                    rules={[{ max: 250, message: 'Please input a description of up to 250 characters' }]}
                    required
                >
                    <Input.TextArea rows={4}  count={{
                        show: true,
                        max: 250,
                    }} />
                </Form.Item>
                <Form.Item
                    name="instructions"
                    label="Agent General Instructions"
                    rules={[{ max: 1000, message: 'Please give general instructions o behavior to your agent.' }, { min: 10, message: 'Please give minimal instructions to your agent.' }]}
                    required
                >
                    <Input.TextArea rows={4} count={{
                        show: true,
                        max: 1000,
                        
                    }} />
                </Form.Item>
                <Form.Item>
                    <SubmitButton form={form}>{(params.mode === 'create') ? 'Create' : 'Save'}</SubmitButton>
                </Form.Item>
            </Form>
        </DrawerTemplate>
    )
}

export default AgentEditDrawer;
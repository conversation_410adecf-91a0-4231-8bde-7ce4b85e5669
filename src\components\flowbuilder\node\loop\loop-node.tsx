'use client';

import { <PERSON><PERSON>, Position } from '@xyflow/react';
import { <PERSON><PERSON>, Card, Flex, Space, Tag } from 'antd';
import React from 'react';
import { SettingOutlined } from '@ant-design/icons';
import { LoopNodeModelData } from './loop-node-model';
import { BaseNodeProps } from '../base-node';
import LoopNodeDescription from './loop-node-description';

export const LoopNodeName = "loop";

function LoopNode(props: BaseNodeProps<LoopNodeModelData>) {
    return (
        <>
            <Card title={<Flex justify='stretch'>
                <div className='reactflow-wrapper'>
                    <Space>
                        {props.data.baseData.name}
                        <Tag color="default" bordered={false}>Loop</Tag>
                    </Space>
                </div>
                <div>
                    <Button
                        style={{ width: '30px' }} type="default"
                        icon={<SettingOutlined />} size='small'
                        onClick={(event) => {
                            event.stopPropagation();
                            props.data.events?.onOpenDrawer();
                        }}
                    />
                </div>
            </Flex>}>
                <LoopNodeDescription {...props.data.baseData} />
            </Card>
            <Handle type="target" position={Position.Left} id="input" />
            <Handle 
                type="source" 
                position={Position.Right} 
                id="loop"
                style={{ top: '33%' }}
            />
            <Handle 
                type="source" 
                position={Position.Right} 
                id="exit"
                style={{ top: '66%' }}
            />
        </>
    );
}

export default React.memo((props: BaseNodeProps<LoopNodeModelData>) => LoopNode(props));

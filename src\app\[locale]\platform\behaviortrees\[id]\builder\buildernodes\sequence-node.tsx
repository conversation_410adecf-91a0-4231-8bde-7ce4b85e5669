'use client';

import { Handle, NodeProps, Position, Node, useUpdateNodeInternals } from '@xyflow/react';
import { Collapse, Form, InputNumber, Space, Tag } from 'antd';
import React from 'react';
import EditableTitle from '@/components/editable-title';
import '@ant-design/v5-patch-for-react-19';
import LimitedHandle from './limited-handle';


type SequenceNodeProps = Node<
  {
    id: string;
    name: string;
    handles?: number;
  }
>

function SequenceNode(props: NodeProps<SequenceNodeProps>) {
  const [componentName, setComponentName] = React.useState(props.data.name);
  const [handleCount, setHandleCount] = React.useState(props.data.handles ?? 3);
  const updateNodeInternals = useUpdateNodeInternals();
  
  React.useEffect(() => {
    updateNodeInternals(props.id);
  }, [handleCount, props.id]);

  const updateHandleCount = React.useCallback((value: number) => {
    setHandleCount(value);
    updateNodeInternals(props.id);
  }, [props.id, updateNodeInternals]);
  
  return (
    <>
    <Handle 
      position={Position.Top}
      type='target'
      title='input'
      />
    {Array.from({ length: handleCount }).map((_, index) => (
        <LimitedHandle
          title={index.toString()}
          key={index}
          type="source"
          position={Position.Bottom}
          id={`handle-${props.id}-${index}`}
          style={{ left: (index % 16) * 20, bottom: -Math.floor(index / 16) * 20 }}
          connectionCount={1}
        />
      ))}
      <Collapse style={{ width: '300px', backgroundColor: '#f0f0f0' }}
        items={[{
          key: '1',
          label: (
            <Space>
              <EditableTitle title={componentName} onTitleChange={setComponentName} editingClassName="nodrag" />
              <Tag color="default">Sequence</Tag></Space>),
          children: (
            <Form.Item label="Items">
              <InputNumber value={handleCount} onChange={(value) => updateHandleCount(parseInt(value?.toString() ?? '3'))} className='nodrag' />
            </Form.Item>
          )
        }]}
      />
    </>
  );
}

export default SequenceNode;
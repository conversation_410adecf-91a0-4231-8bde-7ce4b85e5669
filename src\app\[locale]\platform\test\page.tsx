'use client'

import { BaseNode } from "@/components/flowbuilder/node/base-node";
import { FieldModel } from "@/components/flowbuilder/fields";
import { Behavior<PERSON>reeController } from "@/models/behaviortree";
import { <PERSON><PERSON>, Di<PERSON>r, <PERSON>lex, Result, Skeleton, Space, Steps, Typography } from "antd";
import React from "react";
import { ReactFlowProvider } from "@xyflow/react";
import CreateDnDContext, { DnDProvider } from "@/components/flowbuilder/dnd-context";
import FlowBuilder from "@/components/flowbuilder/flow-builder";
import { SetAgentNodeName } from "@/components/flowbuilder/node/setagent/set-agent-node";
import SetAgentNodeDescription from "@/components/flowbuilder/node/setagent/set-agent-description";
import SetAgentForm from "@/components/flowbuilder/node/setagent/set-agent-form";
import TaskNodeDescription from "@/components/flowbuilder/node/task-node/task-node-description";
import TaskNodeForm from "@/components/flowbuilder/node/task-node/task-node-form";
import { InitializeTaskNodeModelData } from "@/components/flowbuilder/node/task-node/task-node-model";
import { TaskNodeName } from "@/components/flowbuilder/node/task-node/task-node";
import { NodeTypes } from "@/components/flowbuilder/types";
import { RobotOutlined } from '@ant-design/icons';
import SetAgentNode from '@/components/flowbuilder/node/setagent/set-agent-node';
import TaskNode from "@/components/flowbuilder/node/task-node/task-node";


const nodeComponents: NodeTypes = {
    [SetAgentNodeName]: {
        description: SetAgentNodeDescription,
        form: SetAgentForm,
        initializer: () => {return {}},
        node: SetAgentNode,
        icon: <RobotOutlined />,
        name: SetAgentNodeName
    },
    [TaskNodeName]: {
        description: TaskNodeDescription,
        form: TaskNodeForm,
        initializer: InitializeTaskNodeModelData,
        node: TaskNode,
        icon: <RobotOutlined />,
        name: TaskNodeName
    }
};

export default () => {
    const behaviorTreeController = new BehaviorTreeController();
    const { data: behaviorTreeData, isLoading: isLoadingBehaviorTree } = behaviorTreeController.useGet(["2d4317a8-5fae-4b71-b1e8-5e51dbb45bbb"]);
    const DnDContext = React.useMemo(() => CreateDnDContext<any>(), []);
    const NodeTypes = React.useMemo(() => nodeComponents, []);

    const onSave = (nodes : BaseNode<any>[], edges : [], fields : FieldModel[]) => {
        console.log('save');
        console.log(nodes);
        console.log(edges);
        console.log(fields);
        return true;
    }

    if (isLoadingBehaviorTree || !behaviorTreeData) {
        return <Skeleton />
    }

    return (
        <>
            <Flex vertical justify='stretch' style={{ height: '100%' }}>
                <Typography.Title level={2}>
                    Behavior Builder for 'asd'
                </Typography.Title>
                <Divider />
                <ReactFlowProvider>
                    <DnDProvider context={DnDContext}>
                        <FlowBuilder nodes={[]} connections={[]} save={onSave} nodeTypes={NodeTypes} context={DnDContext} />
                    </DnDProvider>
                </ReactFlowProvider>
            </Flex>
        </>
    );
}
import ConditionDescription from "@/components/behaviortree/conditions/condition-description";
import { BaseNode, BaseNodeData, BaseNodeFormElement } from "@/components/behaviortree/node/base-node";
import { FieldModel } from "@/components/flowbuilder/variables/field-model";
import EditableTitle from "@/components/editable-title";
import { EditOutlined, FilterOutlined } from '@ant-design/icons';
import { Button, Divider, Flex, Space } from "antd";
import React from "react";
import { Field, RuleGroupType } from "react-querybuilder";
import EditNodeConditionsDrawer from "./condition-drawer";
import Condition from "@/components/behaviortree/conditions/condition";

export type ViewNodeDrawerProps = {
    nodeId : string;
    nodes: BaseNode<any>[];
    fields : FieldModel[];
    nodeDescription: React.ComponentType<BaseNodeData<any>>;
    nodeForm : React.ComponentType<BaseNodeFormElement<any>>;
    onUpdateNode : (newData : BaseNodeData<any>) => void;
}

type ViewNodeDrawerMode = 'view' | 'properties' | 'conditions';

function FieldsToOptionGroup(fields : FieldModel[]){
    const baseObj = {input: { label: 'Inputs', options: [] as Field[]}, variable: { label: 'Variables', options: [] as Field[]}}
    fields.forEach(fd => baseObj[fd.type].options.push({name: fd.id, label: fd.name}))
    return Object.values(baseObj);
}

function ViewNodeDrawer(props: ViewNodeDrawerProps) {
    const getNodeById = (nodeId : string) => props.nodes.filter(n => n.id == nodeId)[0];
    const currentNodeData = React.useMemo(() => structuredClone(getNodeById(props.nodeId).data.baseData), [props.nodes, props.nodeId])

    const [mode, setMode] = React.useState<ViewNodeDrawerMode>('view')

    if(mode == 'conditions')
        return (
            <Condition 
                fields={FieldsToOptionGroup(props.fields)} 
                query={currentNodeData.nodeConditions} 
                onChange={(newConditions) => {currentNodeData.nodeConditions = newConditions; props.onUpdateNode(currentNodeData)}}
                onCancel={() => {setMode('view')}} />
        )

    if(mode == "properties")
        return (
            <props.nodeForm
                onChange={(newData) => {currentNodeData.nodeData = newData; props.onUpdateNode(currentNodeData)}} 
                onCancel={() => setMode('view')} 
                data={currentNodeData.nodeData} />
        )

    if(mode == 'view')
        return (
            <Space direction="vertical" style={{ width: '100%' }}>
                <EditableTitle title={currentNodeData.name} level={3} onTitleChange={(newTitle) => {currentNodeData.name = newTitle; props.onUpdateNode(currentNodeData)}} />
                <Divider style={{ color: '#ccc' }}>Properties</Divider>
                <props.nodeDescription {...currentNodeData} />
                <Flex justify='center' align='center' gap="middle">
                    <Button type="default" icon={<EditOutlined />} onClick={() => { setMode('properties') }}>Change Properties</Button>
                </Flex>
                <Divider style={{ color: '#ccc' }}>Rules</Divider>
                <ConditionDescription contitionData={currentNodeData.nodeConditions} fields={FieldsToOptionGroup(props.fields)} />
                <Flex justify='center' align='center' gap="middle">
                    <Button type="default" icon={<FilterOutlined />} onClick={() => { setMode('conditions') }}>Change Conditions</Button>
                </Flex>
            </Space>
        )
}

export default ViewNodeDrawer;
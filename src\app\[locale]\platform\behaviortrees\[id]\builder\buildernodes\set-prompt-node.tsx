'use client';

import { useCallback } from 'react';
import { <PERSON>le, NodeProps, Position, Node } from '@xyflow/react';
import { Card, Collapse, Form, Input, InputNumber, Radio, Select, Space, Tag } from 'antd';
import React from 'react';
import { CheckOutlined, EditOutlined } from '@ant-design/icons';
import Paragraph from 'antd/es/typography/Paragraph';
import EditableTitle from '@/components/editable-title';
import { useForm } from 'antd/es/form/Form';
import '@ant-design/v5-patch-for-react-19';
import TextArea from 'antd/es/input/TextArea';

const handleStyle = { left: 10 };


type SetPromptNodeProps = Node<
  {
    name: string;
  }
>

function SetPromptNode(props: NodeProps<SetPromptNodeProps>) {
  const [componentName, setComponentName] = React.useState(props.data.name);
  const [form] = Form.useForm<{ promptKey: string; prompt: string; priority: number }>();


  const [label, setLabel] = React.useState('Agent <PERSON><PERSON>');
  const updateLabel = (value: string) => {
    setLabel(value === 'alias' ? 'Agent <PERSON><PERSON>' : 'Agent Tag');
  }

  return (
    <>
      <Collapse style={{ width: '300px', backgroundColor: '#f0f0f0' }}
        items={[{
          key: '1',
          label: (
            <Space>
              <EditableTitle title={componentName} onTitleChange={setComponentName} editingClassName="nodrag" />
              <Tag color="default">Set Agent</Tag></Space>),
          children: (
            <Form form={form} layout="horizontal" size='small'>
              <Form.Item label="Prompt Key" name="promptKey">
                <Input />
              </Form.Item>
              <Form.Item label="Prompt" name="prompt">
                <TextArea
                  showCount
                  rows={5}
                  maxLength={1000}
                  placeholder="Prompt"
                  style={{ resize: 'none' }}
                />
              </Form.Item>
              <Form.Item label="Priority">
                <InputNumber className='nodrag' />
              </Form.Item>
            </Form>
          )
        }]}
      />
      <Handle type="target" position={Position.Left} id="trigger" />
    </>
  );
}

export default SetPromptNode;
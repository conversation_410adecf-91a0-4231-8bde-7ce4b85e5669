import { Button, Dropdown, MenuProps, Space, TableColumnsType, Tag } from "antd";
import { useTimeAgo } from "next-timeago";
import React from "react";
import { SwapOutlined, DeleteOutlined, EllipsisOutlined, EditOutlined } from "@ant-design/icons";
import DataTable from "@/components/data-table";
import { BehaviorTree, BehaviorTreeController, BehaviorTreeActions } from "@/models/behaviortree";


interface BehaviorTreeListParams {
    onClick: (record: BehaviorTree) => void;
    onEdit: (record: BehaviorTree) => void;
    onBehaviorBuilder: (record: BehaviorTree) => void;
    onDelete: (record: BehaviorTree) => void;
    mutateObjects?: BehaviorTree[];
}



const BehaviorTreeList: React.FC<BehaviorTreeListParams> = (params: BehaviorTreeListParams) => {

    const behaviorTreeController: BehaviorTreeController = new BehaviorTreeController();
    const [pendingData, setPendingData] = React.useState<string[]>([]);
    const [isLoadingData, setIsLoadingData] = React.useState(false);
    const { TimeAgo } = useTimeAgo();

    const contextMenuItems = (record: BehaviorTree): MenuProps['items'] => ([
        {
            label: 'Edit',
            key: 'edit',
            icon: <EditOutlined />,
            onClick: () => { if (params.onEdit) params.onEdit(record); },
            disabled: !behaviorTreeController.can(BehaviorTreeActions.EDIT, record)
        },
        {
            label: 'Behavior Builder',
            key: 'builder',
            icon: <EditOutlined />,
            onClick: () => { if (params.onBehaviorBuilder) params.onBehaviorBuilder(record); },
            disabled: !behaviorTreeController.can(BehaviorTreeActions.BEHAVIORBUILDER, record)
        },
        {
            label: 'Delete',
            key: 'delete',
            icon: <DeleteOutlined />,
            danger: true,
            onClick: () => { if (params.onDelete) params.onDelete(record); },
            disabled: !behaviorTreeController.can(BehaviorTreeActions.DELETE, record)
        },
    ]);

    const contextMenuProps = (record: BehaviorTree) => ({
        items: contextMenuItems(record)
    });

    const columns: TableColumnsType<BehaviorTree> = [
        {
            title: '', render: (_, record) => {
                return (<Space size="small">
                    <Dropdown menu={contextMenuProps(record)}>
                        <a onClick={(e) => e.preventDefault()}>
                            <Space>
                                <Button type='text'><EllipsisOutlined rotate={90} /></Button>
                            </Space>
                        </a>
                    </Dropdown>
                </Space>)
            },
            width: 40,
        },
        { title: 'Name', render: (value, record) => {
            return <Space>
                <a onClick={(e) => {
                    e.preventDefault();
                    if (params.onClick) {
                        params.onClick(record);
                    }
                }}>{record.name}</a>
            </Space>
        }},
        { title: 'Description', dataIndex: 'description' },
        {
            title: 'Last Modified', dataIndex: 'lastModifiedTimestamp', render: ((value, record) => {
                return <TimeAgo date={value * 1000} locale="en-US" live={true} />
            })
        }
    ];

    return (
        <DataTable<BehaviorTree, BehaviorTreeController>
            controller={new BehaviorTreeController()}
            itemUpdateInterval={10000}
            tableColumns={columns}
            onItemsValidatingChange={(isValidating: boolean, validatingIdList: string[]) => {
                setPendingData(validatingIdList);
                setIsLoadingData(isValidating);
            }}
            shouldInvalidate={(entry: BehaviorTree) => false}
            mutateItems={params.mutateObjects}
            rowKey={(record : BehaviorTree) => record.id}
        />
    );
}

export default BehaviorTreeList;

'use client';

import { useCallback } from 'react';
import { Handle, NodeProps, Position, Node, NodeToolbar } from '@xyflow/react';
import { <PERSON><PERSON>, Card, Collapse, Divider, Flex, Form, Input, InputNumber, Radio, Select, Space, Tag, Typography } from 'antd';
import React from 'react';
import { CheckOutlined, EditOutlined, SettingOutlined } from '@ant-design/icons';
import Paragraph from 'antd/es/typography/Paragraph';
import EditableTitle from '@/components/editable-title';
import { useForm } from 'antd/es/form/Form';
import '@ant-design/v5-patch-for-react-19';
import TextArea from 'antd/es/input/TextArea';

const handleStyle = { left: 10 };


type SetPromptNodeProps = Node<
  {
    name: string;
    onEditComponent: (componentEditor: React.JSX.Element) => void;
    onFinish: (properties: NodeProps) => void;
  }
>

function SetPromptNode2(props: NodeProps<SetPromptNodeProps>) {
  const [componentName, setComponentName] = React.useState(props.data.name);
  const [form] = Form.useForm<{ promptKey: string; prompt: string; priority: number }>();

  const [label, setLabel] = React.useState('Agent Alias');
  const updateLabel = (value: string) => {
    setLabel(value === 'alias' ? 'Agent Alias' : 'Agent Tag');
  }

  return (
    <>
      <Collapse style={{ width: '300px', backgroundColor: '#f0f0f0' }}
        items={[{
          key: '1',
          label: (

            <Flex justify='stretch'>
              <div className='reactflow-wrapper'>
                <Space>
                  {componentName}
                  <Tag color="default" bordered={false}>Set Agent</Tag>
                </Space>
              </div>
              <div >
                <Button style={{ width: '30px' }} type="default" icon={<SettingOutlined />} size='small' onClick={(event) => {
                  event.stopPropagation();
                  props.data.onEditComponent(<Button>Hi</Button>)
                  }} />
              </div>
            </Flex>
          ),
          children: (
            <>
              Set agent to <Tag color="blue-inverse" bordered={false}>agent test 33</Tag>alias <Tag color="blue-inverse" bordered={false}>prod</Tag>
            </>
          )
        }]}
      />
      <Handle type="target" position={Position.Left} id="trigger" />
    </>
  );
}

export default SetPromptNode2;
"use client"

import { SetAgentNodeModelData } from '@/components/behaviortree/node/setagent/set-agent-model';
import { SetAgentNodeName } from '@/components/behaviortree/node/setagent/set-agent-node';
import { createContext, useContext, useState, ReactNode } from 'react';

export const NodeDataTypes = {
  [SetAgentNodeName]: {} as SetAgentNodeModelData,
}

type DnDContextType = [keyof typeof NodeDataTypes | null, (type: keyof typeof NodeDataTypes | null) => void];

const DnDContext = createContext<DnDContextType>([null, (_: keyof typeof NodeDataTypes | null) => {}]);

export const DnDProvider = ({ children }: { children: ReactNode }) => {
  const [type, setType] = useState<keyof typeof NodeDataTypes | null>(null);
 
  return (
    <DnDContext.Provider value={[type, setType]}>
      {children}
    </DnDContext.Provider>
  );
}
 
export default DnDContext;
 
export const useDnD = () => {
  return useContext(DnDContext);
}
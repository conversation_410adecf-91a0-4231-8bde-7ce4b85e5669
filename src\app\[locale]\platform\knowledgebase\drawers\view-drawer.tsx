import { KnowledgeBase, KnowledgebaseActions, KnowledgebaseController, KnowledgeBaseStatus } from "@/models/knowledgebase";
import { Alert, AutoComplete, AutoCompleteProps, Button, Card, ConfigProvider, Divider, Flex, Form, Segmented, Space, Spin, Table, TableColumnsType, TableProps, Tabs, Tag, theme, Upload, UploadProps } from "antd";
import {
    ProfileOutlined, DatabaseOutlined, RobotOutlined, EditOutlined,
    RocketOutlined,
    DeleteOutlined,
    LoadingOutlined,
    CheckCircleOutlined,
    ClockCircleOutlined,
    DownloadOutlined,
    InboxOutlined,
    ReloadOutlined,
    DisconnectOutlined,
} from '@ant-design/icons';
import KnowledgebaseDescription from "@/components/knowledgebase/knowledgebase-description";
import React from "react";
import Title from "antd/es/typography/Title";
import { Agent, AgentController } from "@/models/agent";
import AgentDescription, { GetAgentStatusTag } from "@/components/agent/agent-description";
import { KnowledgebaseFile, KnowledgebaseFileController, KnowledgeBaseFileUploadResponse, KnowledgeBaseFileUploadStatus } from "@/models/knowledgebasefile";
import { useTimeAgo } from "next-timeago";
import { formatBytes } from "@/functions/file-size";
import { APIRoutes } from "@/constants";
import Dragger from "antd/es/upload/Dragger";
import { ListResponse } from "@/models/base-model";
import { AgentKnowledgebaseController, AgentKnowledgebaseLinkExists, AgentKnowledgebaseOperation, AgentKnowledgebaseResponse } from "@/models/agentknowledgebase";
import TextArea from "antd/es/input/TextArea";
import AgentKnowledgebaseAssignForm from "@/components/forms/agent-kb-assign-form";


interface KnowledgebaseViewDrawerParams {
    knowledgebase: KnowledgeBase;
    onEdit: (kb: KnowledgeBase) => void;
    onDelete: (kb: KnowledgeBase) => void;
    onDeploy: (kb: KnowledgeBase) => void;
}

interface AssignForm {
    description: string;
}



const KnowledgebaseViewDrawer: React.FC<KnowledgebaseViewDrawerParams> = (params: KnowledgebaseViewDrawerParams) => {

    const knowledgebaseController : KnowledgebaseController = new KnowledgebaseController();
    const agentController: AgentController = new AgentController();
    const knowledgebaseFileController: KnowledgebaseFileController = new KnowledgebaseFileController();
    const agentKnowledgebaseController: AgentKnowledgebaseController = new AgentKnowledgebaseController()


    
    const { data: kbFilesData, isLoading: kbFilesLoading, isValidating: kbFilesValidating, mutate: mutateKbFiles } = knowledgebaseFileController.useList({ kbId: params.knowledgebase.kbId, count: 1000 });
    const [selectedFiles, setSelectedFiles] = React.useState<React.Key[]>([])
    const [kbFileTabState, setKbFileTabState] = React.useState('files')
    const { TimeAgo } = useTimeAgo();
    const { data: kbDeletedFiles, error, trigger: kbFilesDeleteTrigger, isMutating } = knowledgebaseFileController.useDeleteMulti();
    const { data: kbAgentsData, isLoading: kbAgentsLoading, isValidating: kbAgentsValidating, mutate: mutateKbAgents } = agentController.useListFromKnowledgebase({ count: 10 }, params.knowledgebase.kbId );

    const [agentTabState, setAgentTabState] = React.useState('assigned')
    const [autocompleteOptions, setAutocompleteOptions] = React.useState<AutoCompleteProps['options']>([]);
    const [agentSearch, setAgentSearch] = React.useState<string>('')
    const { data: agentSearchData, isLoading: agentSearchIsLoading } = agentController.useSearch(agentSearch, 1)
    const [agentToAssign, setAgentToAssign] = React.useState<Agent | null>(null)
    const { data: existData, isLoading : existLoading, mutate : existMutate } = agentKnowledgebaseController.existsAgentKnowledgebase(params.knowledgebase.kbId, agentToAssign?.agentId);
    const { data: assignAgentData, trigger: assignAgentTrigger, isMutating: assignAgentIsMutating } = agentKnowledgebaseController.assignAgentKnowledgebase();
    

    const {
        token: { colorPrimary },
    } = theme.useToken();

    const kbFilesrowSelection: TableProps<KnowledgebaseFile>['rowSelection'] = {
        onChange: (selectedRowKeys: React.Key[], selectedRows: KnowledgebaseFile[]) => {
            setSelectedFiles(selectedRowKeys)
        },
        getCheckboxProps: (record: KnowledgebaseFile) => ({
            disabled: record.status !== KnowledgeBaseFileUploadStatus.READY, // Column configuration not to be checked
            name: record.fileName,
        }),
    };

    const kbFilesColumns: TableColumnsType<KnowledgebaseFile> = [
        {
            title: 'Name',
            dataIndex: 'fileName',
            key: 'fileName',
        },
        {
            title: 'File Size',
            dataIndex: 'uploadedFileSize',
            render: ((value, record) => formatBytes(value, 1)),
            key: 'uploadedFileSize',
        },
        {
            title: 'Uploaded At',
            dataIndex: 'createdAt',
            render: ((value, record) => {
                return <TimeAgo date={value * 1000} locale="en-US" live={true} />
            }),
            key: 'createdAt',
        },
        {
            title: 'Deployed',
            dataIndex: 'isDeployed',
            render: (value, record) => {
                return (value) ? <Tag icon={<CheckCircleOutlined />} color="success">deployed</Tag> : <Tag color="default">draft</Tag>
            },
            key: 'isDeployed',
        },
        {
            title: 'Download',
            align: 'center',
            render: (value, record) => {
                if (record.status == KnowledgeBaseFileUploadStatus.READY) return <Button icon={<DownloadOutlined />} onClick={() => { knowledgebaseFileController.getDownload(record.fileId, record.fileName); }} type="text" />;
                return "N/A"
            },
            key: 'download',
        },
    ];

    const kbAgentsColumns: TableColumnsType<Agent> = [
        {
            title: 'Name',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: 'Status',
            dataIndex: 'status',
            key: 'status',
            render: ((value, record) => {
                return GetAgentStatusTag(value);
            })
        },
        {
            title: 'Agent Type',
            dataIndex: 'agentType',
            key: 'agentType',
        },
        {
            title: 'Uploaded At',
            dataIndex: 'lastChangeTimestamp',
            render: ((value, record) => {
                return <TimeAgo date={value * 1000} locale="en-US" live={true} />
            }),
            key: 'lastChangeTimestamp',
        },
    ]


    const kbFileUploadProps: UploadProps = {
        name: 'file',
        multiple: true,
        headers: {
            ...knowledgebaseFileController.getUploadHeaders()
        },
        accept: '.csv,.txt',
        action: knowledgebaseFileController.getUploadURL(params.knowledgebase.kbId),
        onChange(info) {
            const { status } = info.file;

            if (status !== 'uploading') {
                console.log(info.file, info.fileList);
            }
            if (status === 'done') {
                console.log(`${info.file.name} file uploaded successfully.`);


                mutateKbFiles(async (data: ListResponse<KnowledgebaseFile> | undefined) => {
                    if (data === undefined) return data;

                    const updatedFiles = info.file.response.entries.map((item: KnowledgebaseFile) => item.fileId);
                    const dataMinusUpdates = data.entries.filter((item: KnowledgebaseFile) => !updatedFiles.includes(item.fileId))
                    dataMinusUpdates.push(...info.file.response.entries);

                    return { ...data, entries: dataMinusUpdates };

                }, { revalidate: false })

                if (info.fileList.filter((f) => f.status !== 'done').length == 0) {
                    setKbFileTabState('files');
                }
            } else if (status === 'error') {
                console.log(`${info.file.name} file upload failed.`);
            }
        },
        onDrop(e) {
            console.log('Dropped files', e.dataTransfer.files);
        },
    };


    React.useEffect(() => {
        setAutocompleteOptions(agentSearchData?.entries.map((entry: Agent) => {
            return {
                label: <AgentDescription agent={entry} layout="horizontal" />,
                value: entry.agentId,
            }
        }))
    }, [agentSearchData])

    React.useEffect(() => {

        if (kbDeletedFiles === undefined) return;
        mutateKbFiles(async (data: ListResponse<KnowledgebaseFile> | undefined) => {
            if (data === undefined) return data;

            const dataMinusDeletes = data.entries.filter((item: KnowledgebaseFile) => !kbDeletedFiles.deletedIds.includes(item.fileId))

            return { ...data, entries: dataMinusDeletes };

        }, { revalidate: false })

    }, [kbDeletedFiles])

    React.useEffect(() => {
        if(agentToAssign !== undefined && agentToAssign != null){
            existMutate();
        }
    }, [agentToAssign])

    
    React.useEffect(() => {

        const assignAgentDataTyped = assignAgentData as AgentKnowledgebaseResponse;
        if (assignAgentDataTyped === undefined) return;

        switch (assignAgentDataTyped.operation) {
            case AgentKnowledgebaseOperation.ASSIGN:
                // add agent to agent list
                mutateKbAgents(async (data: ListResponse<Agent> | undefined) => {
                    if (data === undefined || agentToAssign == null) return data;
                    return { ...data, entries: [...data.entries, agentToAssign] };
                }, { revalidate: false })
                break;
            case AgentKnowledgebaseOperation.UNASSIGN:
                // remove agent from agent list
                mutateKbAgents(async (data: ListResponse<Agent> | undefined) => {
                    if (data === undefined || agentToAssign == null) return data;
                    const filteredData = data.entries.filter((agent) => agent.agentId != assignAgentDataTyped.agentId)
                    return { ...data, entries: filteredData };
                }, { revalidate: false })
                break;
        }
        setAgentTabState('assigned');
        setAgentToAssign(null);
    }, [assignAgentData])


    const kbFilesTabComponent = (kbFileTabState == 'upload') ? (<Dragger {...kbFileUploadProps}>
        <p className="ant-upload-drag-icon">
            <InboxOutlined />
        </p>
        <p className="ant-upload-text">Click or drag file to this area to upload</p>
        <p className="ant-upload-hint">
            Support for a single or bulk upload. Strictly prohibited from uploading company data or other
            banned files.
        </p>
    </Dragger>) : (
        <>

            <Flex align="end" justify="flex-end" style={{ width: '100%', marginBottom: '15px' }}>
                <Space size={"middle"} align="end">
                    <Button type="primary" icon={(kbFilesLoading || kbFilesValidating) ? <LoadingOutlined spin /> : <ReloadOutlined />} disabled={kbFilesLoading || kbFilesValidating} onClick={() => { mutateKbFiles() }}>Refresh</Button>
                    <Button type="primary" icon={(isMutating) ? <LoadingOutlined spin /> : <DeleteOutlined />} disabled={selectedFiles.length == 0 || isMutating} danger onClick={() => {
                        kbFilesDeleteTrigger({ body: { ids: selectedFiles.map((v) => v.toString()) }})
                    }}>Delete Selected Knowledge</Button>
                </Space>
            </Flex>
            <Table<KnowledgebaseFile>
                dataSource={kbFilesData?.entries}
                loading={kbFilesLoading}
                columns={kbFilesColumns}
                rowSelection={{ type: "checkbox", ...kbFilesrowSelection }}
                rowKey='fileId'
            />
        </>
    )

    const agentTabComponentAssignSearch = (<>
        <Title level={5}>Search Agent to assign</Title>
        <Form.Item label="Type 3 or more letters" layout="vertical">
        <AutoComplete
            style={{ width: '100%' }}
            prefix={(agentSearchIsLoading) ? <RobotOutlined style={{ color: 'rgba(0,0,0,.25)' }} /> : <RobotOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
            placeholder="Agent Name"
            options={autocompleteOptions}
            onSelect={(value, option) => {
                setAgentToAssign((agentSearchData?.entries ?? []).filter((agent) => agent.agentId == value)[0])
            }}
            onSearch={(text) => { if(text.length > 1) setAgentSearch(text); else {setAgentSearch('');} }}
            
        />
        </Form.Item>
    </>);

    const agentDescription = (!agentToAssign) ? null : <AgentDescription
        agent={agentToAssign} layout="vertical" />;


    const agentTabComponentAssignFormInstance = (agentToAssign) ? (<AgentKnowledgebaseAssignForm
        disableSubmit={!agentToAssign || (existData && existData.exists)}
        isLoading={assignAgentIsMutating}
        onCancel={() => { setAgentToAssign(null); }}
        onSubmit={(value) => {
            assignAgentTrigger({body: { agentId: agentToAssign?.agentId, kbId: params.knowledgebase.kbId, description: value.description }})
        }}
    />) : (null);

    const agentTabComponentAssignForm = (<>
        {((existData && existData.exists) && !existLoading) ? <Alert
            message="Error"
            description="This Knowledgebase is already assigned to this agent."
            type="error"
            showIcon
            banner
        /> : (null)}
        <Title level={5}>Assigning Knowledgebase</Title>
        <Flex vertical style={{ padding: '15px', backgroundColor: '#eee' }}>
            <Card title="Knowledge Base">
                <KnowledgebaseDescription knowledgebase={params.knowledgebase} layout="horizontal" />
            </Card>
            <Title level={5} style={{ textAlign: 'center' }}>to</Title>
            <Card title="Agent">
                {agentDescription}
            </Card>
            <Title level={5} style={{ textAlign: 'center' }}>with description</Title>
            <Card>
                {agentTabComponentAssignFormInstance}
            </Card>
        </Flex>
    </>)

    const agentTabComponentTable = (
        <>
            <Flex align="end" justify="flex-end" style={{ width: '100%', marginBottom: '15px' }}>
                <Space size={"middle"} align="end">
                    <Button type="primary" icon={(kbFilesLoading || kbFilesValidating) ? <LoadingOutlined spin /> : <ReloadOutlined />} disabled={kbFilesLoading || kbFilesValidating} onClick={() => { mutateKbFiles() }}>Refresh</Button>
                    <Button type="primary" icon={(isMutating) ? <LoadingOutlined spin /> : <DisconnectOutlined />} disabled={selectedFiles.length == 0 || isMutating} danger onClick={() => {
                        console.log("unassign")
                    }}>Unassign Selected Agent</Button>
                </Space>
            </Flex>
            <Table<Agent>
                dataSource={kbAgentsData?.entries}
                loading={kbAgentsLoading}
                columns={kbAgentsColumns}
                rowSelection={{ type: "radio" }}
                rowKey='fileId'
            />
        </>
    )

    const agentTabComponent = (agentTabState == 'assign') ? (<>
        <Flex gap='middle' vertical>
            {(agentToAssign === undefined || agentToAssign == null) ? (agentTabComponentAssignSearch) : (agentTabComponentAssignForm)}
        </Flex>
    </>) : agentTabComponentTable

    return (
        <Tabs
            size="large"
            items={[
                {
                    key: 'Overview',
                    label: 'Overview',
                    icon: <ProfileOutlined />,
                    children: (
                        <Space direction="vertical" style={{ padding: '0 24px' }}>
                            <KnowledgebaseDescription layout="vertical" knowledgebase={params.knowledgebase} />
                            <Divider style={{ color: '#ccc' }}>Actions</Divider>
                            <Flex justify='center' align='center' gap="middle">
                                <Button disabled={!knowledgebaseController.can(KnowledgebaseActions.EDIT, params.knowledgebase)} type="text" icon={<EditOutlined />} onClick={() => { params.onEdit(params.knowledgebase) }}>Edit</Button>
                                <Button type="text" icon={<RocketOutlined />} onClick={() => { params.onDeploy(params.knowledgebase) }}>Deploy</Button>
                                <Button disabled={!knowledgebaseController.can(KnowledgebaseActions.DELETE, params.knowledgebase)} type="primary" danger icon={<DeleteOutlined />} onClick={() => { params.onDelete(params.knowledgebase) }}>Delete</Button>
                            </Flex>
                        </Space>
                    )
                },
                {
                    key: 'Knowledge',
                    label: 'Knowledge',
                    icon: <DatabaseOutlined />,
                    children: (
                        <>
                            <ConfigProvider
                                theme={{
                                    components: {
                                        Segmented: {
                                            itemSelectedColor: '#fff',
                                            itemSelectedBg: colorPrimary,
                                        },
                                    },
                                }}
                            >
                                <Segmented
                                    options={[{ label: 'Knowledge Files', value: 'files' }, { label: 'Upload Knowledge', value: 'upload' }]}
                                    block
                                    onChange={(value) => setKbFileTabState(value)}
                                    value={kbFileTabState} />
                            </ConfigProvider>
                            <Divider />
                            {kbFilesTabComponent}
                        </>
                    )
                },
                {
                    key: 'Agents',
                    label: 'Agents',
                    icon: <RobotOutlined />,
                    children: (
                        <>
                            <ConfigProvider
                                theme={{
                                    components: {
                                        Segmented: {
                                            itemSelectedColor: '#fff',
                                            itemSelectedBg: colorPrimary,
                                        },
                                    },
                                }}
                            >
                                <Segmented
                                    options={[{ label: 'Assigned Agents', value: 'assigned' }, { label: 'Assign to an Agent', value: 'assign' }]}
                                    block
                                    onChange={(value) => setAgentTabState(value)}
                                    value={agentTabState}
                                />
                            </ConfigProvider>
                            <Divider />
                            {agentTabComponent}
                        </>
                    )
                }
            ]}
        />
    )
}

export default KnowledgebaseViewDrawer;
'use client'

import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>er, Space, Typography } from 'antd';
import {
  PlusOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import '@ant-design/v5-patch-for-react-19';
import ConfirmDeleteModal from '@/components/confirm-delete-modal';
import { Agent, AgentController } from '@/models/agent';
import AgentList from './lists/agent-list';
import AgentEditDrawer from './drawers/edit-drawer';
import AgentDeployDrawer from './drawers/deploy-drawer';
import AgentViewDrawer from './drawers/view-drawer';

type AgentComponentProps = any;

type DrawerState = { title: string; isOpen: boolean; component: React.JSX.Element; };
const emptyDrawer: DrawerState = { title: "", isOpen: false, component: <></> }

interface DeleteModalOptions{
  agent? : Agent;
  open: boolean;
}

const AgentComponent: React.FC<AgentComponentProps> = (props: AgentComponentProps) => {
  const [drawerOptions, setDrawerOptions] = React.useState(emptyDrawer);
  const [mutateObject, setMutateObject] = React.useState<Agent[]>([]);
  const [deleteModalOptions, setDeleteModalOptions] = React.useState<DeleteModalOptions>({open: false});

  
  const onCloseDrawer = () => {
    setDrawerOptions(emptyDrawer);
  };

  const onEdit = (record : Agent) => {
    setDrawerOptions(
      {
        title: "Edit Agent",
        isOpen: true,
        component: <AgentEditDrawer
        agent={record}
          isOpen={true}
          onSuccess={(data: Agent) => { setDrawerOptions(emptyDrawer); setMutateObject([data]) }}
          mode='edit'
        />
      }
    )
  }

  const onCreate = () => {
    setDrawerOptions(
      {
        title: "Create an Agent",
        isOpen: true,
        component: <AgentEditDrawer
          isOpen={true}
          onSuccess={(data: Agent) => { setDrawerOptions(emptyDrawer); setMutateObject([data]) }}
          mode='create'
        />
      }
    )
  }

  const onView = (record : Agent) => {
    setDrawerOptions(
      {
        title: "View Knowledge Base",
        isOpen: true,
        component: <AgentViewDrawer 
          agent={record}
          onDelete={(record : Agent) => onDelete(record)}
          onDeploy={(record : Agent) => onDeploy(record)}
          onEdit={(record : Agent) => onEdit(record)}
        />
      }
    )
  }

  const onDeploy = (record : Agent) => {
    setDrawerOptions(
      {
        title: "Deploy Agent",
        isOpen: true,
        component: <AgentDeployDrawer 
          agentId={record.agentId}
        />
      }
    )
  }

  const onDelete = (record : Agent) => {
    setDeleteModalOptions({open: true, agent: record})
  }

  const onDeleteConfirm = (record : Agent) => {
    setDeleteModalOptions({open:false})
    setMutateObject([record])
  }

  const onDeleteCancel = (record : Agent) => {
    setDeleteModalOptions({open:false})
  }


  return (
    <>
      <ConfirmDeleteModal<Agent>
        controller={new AgentController()}
        open={deleteModalOptions.open}
        objectToDelete={deleteModalOptions.agent}
        onDelete={onDeleteConfirm}
        onCancel={onDeleteCancel}
      />
      <Typography.Title level={2}>
        Agents
      </Typography.Title>
      <Button size='large' type='primary' onClick={() => onCreate()}>
        <PlusOutlined />Create
      </Button>
      <Divider />
      <AgentList
        onClick={(record : Agent) => onView(record)}
        onDelete={(record : Agent) => onDelete(record)}
        onDeploy={(record : Agent) => onDeploy(record)}
        onEdit={(record : Agent) => onEdit(record)}
        mutateObjects={mutateObject}
      />
      <Drawer
        title={drawerOptions.title}
        placement="right"
        size="large"
        onClose={onCloseDrawer}
        open={drawerOptions.isOpen}
        extra={
          <Space>
            <Button onClick={onCloseDrawer}>Cancel</Button>
          </Space>
        }
      >
        {drawerOptions.component}
      </Drawer>
    </>
  );
}

export default AgentComponent;
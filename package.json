{"name": "coral-agents-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/nextjs-registry": "^1.0.2", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@react-querybuilder/antd": "^8.3.1", "@react-querybuilder/dnd": "^8.3.1", "@reduxjs/toolkit": "^2.5.1", "@xyflow/react": "^12.4.4", "antd": "^5.23.4", "i18next": "^24.2.2", "i18next-browser-languagedetector": "^8.0.2", "i18next-http-backend": "^3.0.2", "i18next-resources-to-backend": "^1.2.1", "js-file-download": "^0.4.12", "jwt-decode": "^4.0.0", "next": "15.1.6", "next-i18n-router": "^5.5.1", "next-timeago": "^0.2.0", "react": "^19.0.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.0.0", "react-i18next": "^15.4.0", "react-querybuilder": "^8.3.1", "react-redux": "^9.2.0", "swr": "^2.3.2", "universal-cookie": "^7.2.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.6", "typescript": "^5"}}
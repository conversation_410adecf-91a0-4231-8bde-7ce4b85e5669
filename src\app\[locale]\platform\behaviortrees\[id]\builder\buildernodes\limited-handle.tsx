import { Handle, HandleProps, useNodeConnections } from "@xyflow/react";

interface LimitedHandleProps extends HandleProps{
  connectionCount?: number;
}

const LimitedHandle = (props : LimitedHandleProps) => {
    const connections = useNodeConnections({
        handleType: props.type,
        handleId: props.id ?? ''
    });

    const propsMinusConnectionCount = Object.assign({}, props);
    delete propsMinusConnectionCount.connectionCount;

    return (
      <Handle
        {...propsMinusConnectionCount}
        isConnectable={props.connectionCount ? connections.length < props.connectionCount : true}
      />
    );
  };
   
  export default LimitedHandle;
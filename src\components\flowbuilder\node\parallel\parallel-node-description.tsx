import { Tag } from "antd";
import { ParallelNodeModelData } from "./parallel-node-model";
import { BaseNodeData } from "../base-node";

function ParallelNodeDescription(props: BaseNodeData<ParallelNodeModelData>) {
    return (
        <>
            Parallel execution with <Tag color="blue-inverse" bordered={false}>{props.nodeData.outputCount}</Tag> outputs
        </>
    );
}

export default ParallelNodeDescription;

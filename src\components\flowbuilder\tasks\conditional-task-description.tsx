import React from "react";
import { Card, Divider, Space, Typography } from "antd";
import { ConditionalTaskProps } from "./conditional-task";
import ConditionDescription from "@/components/behaviortree/conditions/condition-description";
import { Field, OptionGroup } from "react-querybuilder";
import { BaseTaskProps } from "@/components/flowbuilder/node/task-node/base-task";

interface ConditionalTaskDescriptionProps {
    task: ConditionalTaskProps;
    fields: OptionGroup<Field>[];
    taskDescriptions: Record<string, React.ComponentType<any>>;
}

const ConditionalTaskDescription: React.FC<ConditionalTaskDescriptionProps> = ({ 
    task, 
    fields,
    taskDescriptions
}) => {
    // Render a task description based on task type
    const renderTaskDescription = (task: BaseTaskProps<any>) => {
        const TaskDescriptionComponent = taskDescriptions[task.name.toLowerCase().replace(' ', '-')];
        
        if (!TaskDescriptionComponent) {
            return <Typography.Text>Unknown task type: {task.name}</Typography.Text>;
        }
        
        return <TaskDescriptionComponent task={task} />;
    };

    return (
        <Space direction="vertical" style={{ width: '100%' }}>
            {task.taskData.clauses.map((clause, index) => (
                <Card 
                    key={index} 
                    size="small"
                    title={index === 0 ? 'IF' : 'ELSE IF'}
                >
                    <Typography.Text type="secondary">Condition:</Typography.Text>
                    <ConditionDescription 
                        contitionData={clause.condition} 
                        fields={fields} 
                    />
                    
                    {clause.tasks.length > 0 && (
                        <>
                            <Divider plain>Tasks</Divider>
                            <Space direction="vertical" style={{ width: '100%' }}>
                                {clause.tasks.map((subTask, taskIndex) => (
                                    <Card 
                                        key={taskIndex} 
                                        size="small" 
                                        title={`Task ${taskIndex + 1}: ${subTask.name}`}
                                    >
                                        {renderTaskDescription(subTask)}
                                    </Card>
                                ))}
                            </Space>
                        </>
                    )}
                </Card>
            ))}
            
            {task.taskData.elseTasks.length > 0 && (
                <Card size="small" title="ELSE">
                    <Space direction="vertical" style={{ width: '100%' }}>
                        {task.taskData.elseTasks.map((subTask, taskIndex) => (
                            <Card 
                                key={taskIndex} 
                                size="small" 
                                title={`Task ${taskIndex + 1}: ${subTask.name}`}
                            >
                                {renderTaskDescription(subTask)}
                            </Card>
                        ))}
                    </Space>
                </Card>
            )}
            
            {task.taskData.clauses.length === 0 && task.taskData.elseTasks.length === 0 && (
                <Typography.Text type="secondary">No conditions configured</Typography.Text>
            )}
        </Space>
    );
};

export default ConditionalTaskDescription;
import { Tag } from "antd";
import { SequenceNodeModelData } from "./sequence-node-model";
import { BaseNodeData } from "../base-node";

function SequenceNodeDescription(props: BaseNodeData<SequenceNodeModelData>) {
    return (
        <>
            Sequence execution with <Tag color="blue-inverse" bordered={false}>{props.nodeData.outputCount}</Tag> outputs
        </>
    );
}

export default SequenceNodeDescription;

import { BehaviorTreeController } from "@/models/behaviortree";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>kel<PERSON>, <PERSON>, Typography } from "antd";
import React from "react";
import {
    PlusOutlined,
} from '@ant-design/icons';
import '@ant-design/v5-patch-for-react-19';


import BehaviorTreeBuilder from "./builder2";


export default async function BehaviorBuilderPage({
    params,
  }: {
    params: Promise<{ id: string }>
  }) {

    const id = (await params).id;
    
    return (
        <BehaviorTreeBuilder id={id} />
    )
}
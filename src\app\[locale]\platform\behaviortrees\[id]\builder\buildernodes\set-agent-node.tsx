'use client';

import { useCallback } from 'react';
import { <PERSON><PERSON>, NodeProps, Position, Node } from '@xyflow/react';
import { Card, Collapse, Form, Input, Radio, Select, Space, Tag } from 'antd';
import React from 'react';
import { CheckOutlined, EditOutlined } from '@ant-design/icons';
import Paragraph from 'antd/es/typography/Paragraph';
import EditableTitle from '@/components/editable-title';
import { useForm } from 'antd/es/form/Form';
import '@ant-design/v5-patch-for-react-19';

const handleStyle = { left: 10 };


type SetAgentNodeProps = Node<
  {
    name: string;
  }
>

function SetAgentNode(props: NodeProps<SetAgentNodeProps>) {
  const [componentName, setComponentName] = React.useState(props.data.name);
  const [form] = Form.useForm<{ agentId: string; agentType: string; agentTagOrAlias: string }>();

  
  const [label, setLabel] = React.useState('Agent Alias');
  const updateLabel = (value: string) => {
    setLabel(value === 'alias' ? 'Agent Alias' : 'Agent Tag');
  }

  return (
    <>
      <Collapse style={{ width: '300px', backgroundColor: '#f0f0f0' }}
        items={[{
          key: '1',
          label: (
            <Space>
              <EditableTitle title={componentName} onTitleChange={setComponentName} editingClassName="nodrag" level={3} />
              <Tag color="default">Set Agent</Tag></Space>),
          children: (
            <Form form={form} layout="horizontal" size='small'>
              <Form.Item label="Agent" name="agentId">
                <Input placeholder="Search Agent" />
              </Form.Item>
              <Form.Item label="Call Agent By" name="agentType">
                <Radio.Group value='alias' onChange={(e) => {
                  updateLabel(e.target.value);
                }}>
                  <Radio.Button value="alias">Alias</Radio.Button>
                  <Radio.Button value="tag">Tag</Radio.Button>
                </Radio.Group>
              </Form.Item>
              <Form.Item label={label} name="agentTagOrAlias">
                <Select className='nodrag'>
                  <Select.Option value="demo">Demo</Select.Option>
                </Select>
              </Form.Item>
            </Form>
          )
        }]}
      />
      <Handle type="target" position={Position.Left} id="trigger" />
    </>
  );
}

export default SetAgentNode;
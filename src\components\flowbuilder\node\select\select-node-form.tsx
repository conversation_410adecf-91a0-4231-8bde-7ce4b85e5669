import { Form, InputNumber, Space } from "antd";
import { BaseNodeFormElement } from "../base-node";
import { SelectNodeModelData } from "./select-node-model";
import React from "react";
import SubmitButton from "@/components/submit-button";

export interface SelectNodeFormProps extends BaseNodeFormElement<SelectNodeModelData> {
}

function submitForm(form: any): SelectNodeModelData {
    const values = form.getFieldsValue();
    return {
        outputCount: values.outputCount || 2
    };
}

export default function SelectNodeForm(props: SelectNodeFormProps) {
    const [form] = Form.useForm<SelectNodeModelData>();

    return (
        <Form 
            form={form} 
            layout="vertical" 
            onFinish={() => {props.onChange(submitForm(form)); props.onCancel()}}
            initialValues={props.data}
        >
            <Form.Item 
                label="Number of Outputs" 
                name="outputCount"
                rules={[
                    { required: true, message: 'Please enter number of outputs' },
                    { type: 'number', min: 1, max: 10, message: 'Must be between 1 and 10' }
                ]}
            >
                <InputNumber 
                    min={1} 
                    max={10} 
                    placeholder="Number of outputs"
                    style={{ width: '100%' }}
                />
            </Form.Item>
            <Space>
                <SubmitButton form={form}>Save</SubmitButton>
            </Space>
        </Form>
    );
}

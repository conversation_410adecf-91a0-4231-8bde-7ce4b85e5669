import Condition from "@/components/behaviortree/conditions/condition";
import { Button, Space } from "antd";
import React from "react";
import { Field, OptionGroup, RuleGroupType } from "react-querybuilder";

interface EditNodeConditionsDrawerProps{
    nodeConditions? : RuleGroupType;
    treeFields: OptionGroup<Field>[];
    onChangeConditions : (conditions : RuleGroupType) => void;
    onCancel: () => void;
}

function EditNodeConditionsDrawer(props: EditNodeConditionsDrawerProps) {
    const [query, setQuery] = React.useState<RuleGroupType>(props.nodeConditions ? structuredClone(props.nodeConditions) : { combinator: 'and', rules: [] });

    return (
        <Space>
            <Condition fields={props.treeFields} query={query} onChange={setQuery} onCancel={props.onCancel} />
            <Button onClick={() => props.onChangeConditions(query)}>Save</Button>
        </Space>
    )
}

export default EditNodeConditionsDrawer;
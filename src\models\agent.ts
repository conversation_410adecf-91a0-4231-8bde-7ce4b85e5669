import { KeyedMutator, SWRConfiguration } from "swr";
import { IBasemodel, IBasemodelController, IBaseModelSearchController, ListRequest, ListResponse } from "./base-model";
import { APIRoutes } from "@/constants";
import { Fetcher2 } from "@/functions/fetcher2";
import { AgentTag } from "./agenttag";
import { Agent<PERSON>lias } from "./agentalias";
import { features } from "process";


export enum AgentStatus {
    QUEUED = 'QUEUED',
    DB_ENTRY_CREATED = 'DB_ENTRY_CREATED',
    CREATING = 'CREATING',
    UPDATING = 'UPDATING',
    READY = 'READY',
    SEALING = 'SEALING',
    PREPARING = 'PREPARING',
    VERSIONING = 'VERSIONING',
    WAITING_VERSION = 'WAITING_VERSION',
    TAGGING = 'TAGGING',
    Unknown = ''
}

export enum AgentType {
    Unknown = 'Unknown',
    Claude_v2 = 'Claude_v2'
}

export interface Agent extends IBasemodel {
    lastChangeTimestamp: number;
    createdAt: number;
    agentId: string;
    name: string;
    description: string;
    instructions: string;
    agentType: AgentType;
    status: AgentStatus;
}

export enum AgentActions {
    EDIT,
    DELETE,
    DEPLOY,
}

export class AgentController implements IBasemodelController<Agent, AgentActions>, IBaseModelSearchController<Agent> {
    useList = (request: ListRequest) => Fetcher2.SWRTemplate<ListResponse<Agent>>(APIRoutes.AgentController.LIST, { method: 'GET', queryString: request })

    useDelete = (id: string) => Fetcher2.SWRMutationTemplate<Agent>(APIRoutes.AgentController.DELETE, { method: 'DELETE', urlPlaceholders: { agentId: id } })


    can = (action: AgentActions, onItem: Agent) => {
        return onItem.status == AgentStatus.READY;
    }

    getId: (agent: Agent) => string = (agent) => agent.agentId;

    useGet = (ids: string[], options?: SWRConfiguration) => Fetcher2.SWRMultiTemplate<Agent>(ids.map((id) => APIRoutes.AgentController.GET.replace("{agentId}", id)), { method: 'GET' }, options)

    useSearch = (query?: string, limit: number = 1) => {
        return Fetcher2.SWRTemplateRaw<ListResponse<Agent>>(
            APIRoutes.AgentController.SEARCH,
            {
                method: 'GET', queryString: { query: query, limit: limit }
            },
            {
                revalidateOnMount: false,
                keepPreviousData: true
            }
        )
    }

    useSearchMutator = (query?: string, limit: number = 1) => {
        return Fetcher2.SWRMutationTemplate<ListResponse<Agent>>(
            APIRoutes.AgentController.SEARCH,
            {
                method: 'GET', queryString: { query: query, limit: limit }
            }
        )
    }

    useListFromKnowledgebase = (request: ListRequest, kbId: string) => Fetcher2.SWRTemplate<ListResponse<Agent>>(
        APIRoutes.KnowledgeBaseController.LIST_AGENTS_FOR_KNOWLEDGEBASE,
        { method: 'GET', urlPlaceholders: { kbId: kbId }, queryString: request }
    )

    useUpdate = (agentId?: string) => Fetcher2.SWRMutationTemplate<Agent>(APIRoutes.AgentController.UPDATE, { method: 'PUT', urlPlaceholders: { agentId: agentId } });

    useDeploy = (agentId: string) => Fetcher2.SWRMutationTemplate<Agent>(APIRoutes.AgentController.DEPLOY, { method: 'POST', urlPlaceholders: { agentId: agentId } })

    useListTags = (request: ListRequest, agentId?: string) => {
        return Fetcher2.SWRTemplate<ListResponse<AgentTag>>(
            APIRoutes.AgentController.LIST_TAGS,
            { method: 'GET', urlPlaceholders: { agentId: agentId }, queryString: request },
            {
                revalidateOnMount: false
            }
        )
    }

    useCreateAlias = () => Fetcher2.SWRMutationTemplate<AgentAlias>(APIRoutes.AgentController.CREATE_ALIAS, { method: 'POST' })

    useListAliases = (request: ListRequest, agentId? : string) => Fetcher2.SWRTemplate<ListResponse<AgentAlias>>(
        APIRoutes.AgentController.LIST_ALIASES,
        { method: 'GET', urlPlaceholders: { agentId: agentId }, queryString: request },
        {
            revalidateOnMount: false
        }
    )
}
'use client'

import { Behavior<PERSON>ree, BehaviorTreeController } from "@/models/behaviortree";
import BehaviorTreeList from "./list/behaviortree-list";
import { <PERSON><PERSON>, Divider, Drawer, Space, Typography } from "antd";
import ConfirmDeleteModal from "@/components/confirm-delete-modal";
import React from "react";
import {
    PlusOutlined,
} from '@ant-design/icons';
import '@ant-design/v5-patch-for-react-19';
import BehaviorTreePutDrawer from "./drawers/put-drawer";
import { useRouter } from "next/navigation";
type ApiKeysComponentProps = any;

type DrawerState = { title: string; isOpen: boolean; component: React.JSX.Element; };
const emptyDrawer: DrawerState = { title: "", isOpen: false, component: <></> }

interface DeleteModalOptions {
    behaviorTree?: BehaviorTree;
    open: boolean;
}

const ApiKeysComponent: React.FC<ApiKeysComponentProps> = (props: ApiKeysComponentProps) => {
    const [drawerOptions, setDrawerOptions] = React.useState(emptyDrawer);
    const [mutateObject, setMutateObject] = React.useState<BehaviorTree[]>([]);
    const [deleteModalOptions, setDeleteModalOptions] = React.useState<DeleteModalOptions>({ open: false });
    const router = useRouter();

    const onCloseDrawer = () => {
        setDrawerOptions(emptyDrawer);
    };

    const onCreate = () => {
        setDrawerOptions({ title: "Create Behavior Tree", isOpen: true, component: <BehaviorTreePutDrawer isOpen={true} onSuccess={onSuccess} mode='create' /> })
    }

    const onView = (record: BehaviorTree) => {
        //setDrawerOptions({ title: "View Behavior Tree", isOpen: true, component: <BehaviorTreeViewDrawer data={record} onSwap={onSwap} onDelete={onDelete} /> })
    }

    const onBehaviorBuilder = (record: BehaviorTree) => {
        router.push(`/platform/behaviortrees/${record.id}/behaviorbuilder`);
    }

    const onEdit = (record: BehaviorTree) => {
        setDrawerOptions({ title: "Edit Behavior Tree", isOpen: true, component: <BehaviorTreePutDrawer isOpen={true} onSuccess={onSuccess} mode='edit' behaviorTree={record} /> })
    }

    const onSuccess = (record: BehaviorTree) => {
        setMutateObject([record])
    }


    const onDelete = (record: BehaviorTree) => {
        setDeleteModalOptions({ open: true, behaviorTree: record })
    }

    const onDeleteConfirm = (record: BehaviorTree) => {
        setDeleteModalOptions({ open: false })
        setMutateObject([record])
    }

    const onDeleteCancel = (record: BehaviorTree) => {
        setDeleteModalOptions({ open: false })
    }

    return (
        <>
            <ConfirmDeleteModal<BehaviorTree>
                controller={new BehaviorTreeController()}
                open={deleteModalOptions.open}
                objectToDelete={deleteModalOptions.behaviorTree}
                onDelete={onDeleteConfirm}
                onCancel={onDeleteCancel}
            />
            <Typography.Title level={2}>
                Behavior Trees
            </Typography.Title>
            <Button size='large' type='primary' onClick={() => onCreate()}>
                <PlusOutlined />Create
            </Button>
            <Divider />
            <BehaviorTreeList
                onClick={(record) => onView(record)}
                onDelete={(record) => onDelete(record)}
                onEdit={(record) => onEdit(record)}
                onBehaviorBuilder={(record) => onBehaviorBuilder(record)}
                mutateObjects={mutateObject}
            />
            <Drawer
                title={drawerOptions.title}
                placement="right"
                size="large"
                onClose={onCloseDrawer}
                open={drawerOptions.isOpen}
                extra={
                    <Space>
                        <Button onClick={onCloseDrawer}>Cancel</Button>
                    </Space>
                }
            >
                {drawerOptions.component}
            </Drawer>
        </>
    )
}


export default ApiKeysComponent;
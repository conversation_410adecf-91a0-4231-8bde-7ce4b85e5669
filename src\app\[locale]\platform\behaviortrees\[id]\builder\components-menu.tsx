import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Segmented, Space, Tabs, theme } from 'antd';
import { useDnD } from './DnDContext';
import { RobotOutlined, ReconciliationOutlined } from '@ant-design/icons';
import { SetAgentNodeName } from '@/components/behaviortree/node/setagent/set-agent-node';
import { Position } from '@xyflow/react';
import React from 'react';
import VariablesForm from '@/components/flowbuilder/variables/variables-form';
import { FieldModel, FieldModelType } from '@/components/flowbuilder/variables/field-model';
import { TaskNodeName } from '@/components/behaviortree/node/task/task-node';

const items = [
  {
    nodeType: SetAgentNodeName,
    nodeName: "Set Agent",
    nodeIcon: <RobotOutlined />
  },
  {
    nodeType: TaskNodeName,
    nodeName: "Task",
    nodeIcon: <ReconciliationOutlined />
  }
]

interface ComponentsMenuProps{
  onChangeField : (values : FieldModel) => void;
  fields: FieldMode<PERSON>[];
}

interface FormatedField{
  title: string;
  description: string;
}

type FormatedFieldsObject = {[key in FieldModelType]: FormatedField[];}

const formatFields : (fields : FieldModel[]) => FormatedFieldsObject = (fields) => {
    return {
      variable: fields.filter(f => f.type == 'variable').map(f => ({"title": f.name, "description": `${f.dataType} - Initial Value: ${f.defaultValue ?? 'null'}`})), 
      input: fields.filter(f => f.type == 'input').map(f => ({"title": f.name, "description": `${f.dataType} - Default Value: ${f.defaultValue ?? 'null'} - Required: ${f.required}`}))
    }
}

export default (props : ComponentsMenuProps) => {
  const [_, setType] = useDnD();
  const [viewType, setViewType] = React.useState<FieldModelType>('input')
  const [showingList, setShowingList] = React.useState(true)
  const [fieldsData, setFieldsData] = React.useState(formatFields(props.fields))

  const {
    token: { colorPrimary },
  } = theme.useToken();

  const onDragStart = (event, nodeType) => {
    setType(nodeType);
  };


  React.useEffect(() => {
    setFieldsData(formatFields(props.fields))
  }, [props.fields])

  return (
    <Tabs
      defaultActiveKey="1"
      type="card"
      size='middle'
      items={[
        {
          label: `Components`,
          key: 'Components',
          children: <List
            dataSource={items}
            style={{ padding: '15px' }}
            renderItem={(item) => (
              <List.Item>
                <div className="dndnode" onDragStart={(event) => onDragStart(event, `${item.nodeType}`)} draggable>
                  <Space direction='horizontal'>
                    {item.nodeIcon}
                    {item.nodeName}
                  </Space>
                </div>
              </List.Item>
            )}
          />,
        },
        {
          label: `Inputs & Variables`,
          key: 'Inputs',
          children: <Flex vertical style={{width:'100%'}} align='center'>
            <ConfigProvider
              theme={{
                components: {
                  Segmented: {
                    itemSelectedColor: '#fff',
                    itemSelectedBg: colorPrimary,
                  },
                },
              }}
            >
            <Segmented options={['input', 'variable']} onChange={setViewType} value={viewType} style={{margin:'10px'}} />
            </ConfigProvider>
            <VariablesForm formType={viewType} onClose={() => { setShowingList(true); }} onShow={() => { setShowingList(false) }} onSave={(values) => {
              props.onChangeField(values)
             }}
             fields={props.fields}
             
             />
            {showingList ? (<>
            <Divider style={{color:'#ccc'}}>{viewType=='input' ? "Inputs" : "Variables"}</Divider>
            <List
              style={{width:'100%', margin:'10px'}}
              pagination={{ position: 'bottom', align: 'center', pageSize:5 }}
              dataSource={fieldsData[viewType]}
              renderItem={(item, index) => (
                <List.Item>
                  <List.Item.Meta
                    title={item.title}
                    description={item.description}
                  />
                </List.Item>
              )}
            /></>) : null}</Flex>,
        }
      ]}
    />

  );
};